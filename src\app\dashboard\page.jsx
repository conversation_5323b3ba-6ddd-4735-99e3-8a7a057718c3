'use client';

import { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import { CONFIG } from '@/data/templates';
import { DOPPLER_NAMES } from '@/data/constants';

export default function Dashboard() {
  const [data, setData] = useState([]);
  const [loading, setLoading] = useState(false); // 改为false，不显示loading状态
  const [dataLoading, setDataLoading] = useState(false); // 新增：专门用于数据加载状态
  const [error, setError] = useState(null);
  const [currentTier, setCurrentTier] = useState('T1');

  // 添加数据缓存，按等级存储
  const [dataCache, setDataCache] = useState({
    T1: [],
    T2: []
  });

  const [expandedSections, setExpandedSections] = useState({});
  const [searchQuery, setSearchQuery] = useState('');
  const [lastRefreshTime, setLastRefreshTime] = useState(new Date());
  const [nextRefreshTime, setNextRefreshTime] = useState(null);
  const [searchInput, setSearchInput] = useState('');
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const [premiumItemsCount, setPremiumItemsCount] = useState(0);

  // Phase专属搜索状态
  const [phaseFilters, setPhaseFilters] = useState({});

  // 认证相关状态
  const { user, isAuthenticated, hasValidSubscription, getSubscriptionInfo, logout, loading: authLoading, fetchUserProfile } = useAuth();
  const router = useRouter();

  // 检查登录状态，未登录重定向到登录页
  useEffect(() => {
    if (!authLoading && !isAuthenticated) {
      router.push('/login');
    }
  }, [authLoading, isAuthenticated, router]);

  // 检查订阅状态，如果没有有效订阅则刷新用户数据
  useEffect(() => {
    if (isAuthenticated && !hasValidSubscription()) {
      // 如果用户已登录但没有有效订阅，尝试刷新用户数据
      fetchUserProfile();
    }
  }, [isAuthenticated]);

  // 切换等级时显示数据
  useEffect(() => {
    // 立即清空当前数据，避免显示错误的数据
    setData([]);

    // 检查是否有缓存数据
    if (hasValidSubscription() && dataCache[currentTier] && dataCache[currentTier].length > 0) {
      // 使用缓存数据
      setData(dataCache[currentTier]);
    } else if (hasValidSubscription()) {
      // 如果是会员但没有缓存数据，调用API获取
      setDataLoading(true);
      fetchTemplates(currentTier);
    } else {
      // 非会员显示示例数据，传入当前tier确保数据正确
      setData(generateSampleData(currentTier));
    }
    setLoading(false);
  }, [currentTier, dataCache, hasValidSubscription]); // 当等级变化或缓存更新时重新设置数据

  // 初始化时获取数据（仅在首次加载时）
  useEffect(() => {
    // 只有会员用户才调用API获取真实数据，且仅在首次加载时
    if (isAuthenticated && hasValidSubscription()) {
      setDataLoading(true);
      fetchTemplates(currentTier);
    }
  }, [isAuthenticated, user]); // 移除currentTier依赖，避免切换tab时重新调用API

  // 生成示例数据供非会员用户查看页面结构
  const generateSampleData = (tier = currentTier) => {
    const sampleData = [];

    // 获取指定等级的模板配置
    const templates = CONFIG[`templates${tier}`] || {};

    // 遍历所有模板，生成示例数据
    Object.entries(templates).forEach(([templateName, variants]) => {
      variants.forEach(variant => {
        const dopplerProperty = variant.dopplerProperty || variant.wearLevel || 0;
        sampleData.push({
          templateName,
          dopplerName: DOPPLER_NAMES[dopplerProperty] || `Phase ${dopplerProperty}`,
          dopplerProperty,
          basePrice: 0,
          floorPrice: 0,
          items: [] // 非会员用户不显示具体商品数据
        });
      });
    });

    return sampleData;
  };

  // 计算下次刷新时间
  const getNextRefreshTime = () => {
    const now = new Date();
    const currentMinutes = now.getMinutes();
    const nextRefresh = new Date(now);

    if (currentMinutes < 30) {
      // 如果当前时间小于30分钟，下次刷新是本小时的30分
      nextRefresh.setMinutes(30, 0, 0);
    } else {
      // 如果当前时间大于等于30分钟，下次刷新是下一小时的0分
      nextRefresh.setHours(nextRefresh.getHours() + 1, 0, 0, 0);
    }

    return nextRefresh;
  };

  // 自动刷新功能 - 在半点和整点时刷新（仅限会员）
  useEffect(() => {
    if (!isAuthenticated || !hasValidSubscription()) return;

    const checkAndRefresh = () => {
      const now = new Date();
      const minutes = now.getMinutes();
      const seconds = now.getSeconds();

      // 在整点或半点的前5秒内触发刷新（避免重复刷新）
      if ((minutes === 0 || minutes === 30) && seconds < 5) {
        // 刷新当前等级的数据
        fetchTemplates(currentTier);
        setLastRefreshTime(new Date());
      }

      // 更新下次刷新时间
      setNextRefreshTime(getNextRefreshTime());
    };

    // 每分钟检查一次是否需要刷新
    const interval = setInterval(checkAndRefresh, 60 * 1000); // 1分钟检查一次

    // 立即检查一次（如果当前就是刷新时间）
    checkAndRefresh();

    return () => clearInterval(interval);
  }, [isAuthenticated]); // 移除currentTier依赖

  const fetchTemplates = async (tier) => {
    try {
      setDataLoading(true);
      setError(null); // 清除之前的错误

      // 添加更详细的错误处理和调试信息
      const url = `/api/templates?tier=${tier}`;

      const response = await fetch(url, {
        method: 'GET',
        headers: {
          'Accept': 'application/json',
          'Content-Type': 'application/json',
        },
        // 添加缓存控制
        cache: 'no-cache'
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`HTTP ${response.status}: ${response.statusText} - ${errorText}`);
      }

      const result = await response.json();

      if (result.code === 0) {
        // 更新缓存
        setDataCache(prev => ({
          ...prev,
          [tier]: result.data.results
        }));

        // 如果当前显示的是这个等级，则更新显示数据
        if (tier === currentTier) {
          setData(result.data.results);
        }

        setLastRefreshTime(new Date());
        // 更新溢价商品数量
        const dataWithPremium = result.data;
        if (dataWithPremium.premiumItemsCount !== undefined) {
          setPremiumItemsCount(dataWithPremium.premiumItemsCount);
        }
      } else {
        setError(result.msg || '未知错误');
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : '未知错误';
      setError('获取数据失败: ' + errorMessage);
    } finally {
      setDataLoading(false);
    }
  };

  // 手动刷新函数（仅限会员）
  const handleManualRefresh = () => {
    if (hasValidSubscription()) {
      fetchTemplates(currentTier);
    } else {
      alert('此功能需要会员订阅，请先订阅会员');
    }
  };



  // 查询溢价商品
  const handlePremiumQuery = async () => {
    try {
      setDataLoading(true);
      const response = await fetch(`/api/premium-items?tier=${currentTier}`);
      const result = await response.json();

      if (result.code === 0) {
        const count = result.data.premiumItemsCount;
        const percentage = result.data.maxPremiumPercentage;

        if (result.data.configEnabled === false) {
          alert(`⚠️ 溢价筛选功能已禁用`);
        } else {
          alert(`✅ 溢价查询完成！\n找到 ${count} 个符合条件的商品\n溢价限制: ≤ ${percentage}\n通知已发送到企业微信群`);
        }
      } else {
        alert('❌ 溢价查询失败：' + result.msg);
      }
    } catch (error) {
      alert('❌ 溢价查询时出错：' + error.message);
    } finally {
      setDataLoading(false);
    }
  };



  // 自定义饰品排序函数
  const getTemplateSortOrder = (templateName) => {
    // 定义优先级排序规则
    const sortOrder = {
      '蝴蝶刀（★） | 多普勒': 1,
      '蝴蝶刀（★） | 伽玛多普勒': 2,
      '爪子刀（★） | 多普勒': 3,
      '爪子刀（★） | 伽玛多普勒': 4,
      'M9 刺刀（★） | 多普勒': 5,
      'M9 刺刀（★） | 伽玛多普勒': 6,
    };

    // 如果在排序规则中找到，返回对应的优先级，否则返回999（排在最后）
    return sortOrder[templateName] || 999;
  };

  // 获取所有可用的饰品类型并按自定义规则排序
  const availableTemplates = Array.from(new Set(data.map(template => template.templateName)))
    .sort((a, b) => {
      const orderA = getTemplateSortOrder(a);
      const orderB = getTemplateSortOrder(b);

      // 如果两个都有自定义排序，按优先级排序
      if (orderA !== 999 && orderB !== 999) {
        return orderA - orderB;
      }

      // 如果只有一个有自定义排序，有排序的排在前面
      if (orderA !== 999) return -1;
      if (orderB !== 999) return 1;

      // 如果都没有自定义排序，按字母顺序排序
      return a.localeCompare(b);
    });

  // 根据输入过滤饰品类型（模糊搜索）
  const filteredTemplates = availableTemplates.filter(template =>
    template.toLowerCase().includes(searchInput.toLowerCase())
  );

  // 搜索过滤函数
  const filteredData = searchQuery === ''
    ? data
    : data.filter(template => template.templateName === searchQuery);

  // 处理选择饰品类型
  const handleSelectTemplate = (templateName) => {
    setSearchQuery(templateName);
    setSearchInput(templateName || '');
    setIsDropdownOpen(false);
  };

  // 处理清除搜索
  const handleClearSearch = () => {
    setSearchQuery('');
    setSearchInput('');
    setIsDropdownOpen(false);
  };

  // 处理Phase过滤
  const updatePhaseFilter = (templateName, dopplerProperty, filterType, value) => {
    const key = `${templateName}-${dopplerProperty}`;
    setPhaseFilters(prev => ({
      ...prev,
      [key]: {
        ...prev[key],
        templateFilter: filterType === 'template' ? value : (prev[key]?.templateFilter || ''),
        wearFilter: filterType === 'wear' ? value : (prev[key]?.wearFilter || '')
      }
    }));
  };

  // 获取过滤后的商品列表
  const getFilteredItems = (items, templateName, dopplerProperty) => {
    const key = `${templateName}-${dopplerProperty}`;
    const filters = phaseFilters[key];

    if (!filters) return items;

    let filtered = items;

    // 模板过滤
    if (filters.templateFilter) {
      filtered = filtered.filter(item =>
        (item.paintSeed || item.templateId || '').toString().includes(filters.templateFilter)
      );
    }

    // 磨损过滤
    if (filters.wearFilter) {
      // 解析磨损范围字符串，例如 "0.00-0.01" -> [0.00, 0.01]
      const [minStr, maxStr] = filters.wearFilter.split('-');
      const minWear = parseFloat(minStr);
      const maxWear = parseFloat(maxStr);

      if (!isNaN(minWear) && !isNaN(maxWear)) {
        filtered = filtered.filter(item => {
          const wear = parseFloat(item.abrade || '0');
          // 对于最后一个区间 (如 0.90-1.00)，包含上边界
          if (maxWear === 1.00) {
            return wear >= minWear && wear <= maxWear;
          }
          return wear >= minWear && wear < maxWear;
        });
      }
    }

    return filtered;
  };

  // 点击外部关闭下拉框
  useEffect(() => {
    const handleClickOutside = (event) => {
      const target = event.target;
      if (!target?.closest('.search-dropdown')) {
        setIsDropdownOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  const handleTierChange = (tier) => {
    // 立即清空当前数据，避免显示错误的数据
    setData([]);
    setCurrentTier(tier);
    setExpandedSections({}); // 清空展开状态
    setSearchQuery(''); // 清空搜索状态
    setSearchInput(''); // 清空搜索输入
    setPhaseFilters({}); // 清空Phase过滤器
    // 数据设置由useEffect自动处理，不在这里调用API
  };

  const toggleExpanded = (sectionKey) => {
    setExpandedSections(prev => ({
      ...prev,
      [sectionKey]: !prev[sectionKey]
    }));
  };

  // 按模板分组并排序
  const groupedData = Object.entries(
    filteredData.reduce((groups, template) => {
      if (!groups[template.templateName]) {
        groups[template.templateName] = [];
      }
      groups[template.templateName].push(template);
      return groups;
    }, {})
  ).map(([templateName, templates]) => {
    // 按 dopplerProperty 或 wearLevel 排序
    const sortedTemplates = templates.sort((a, b) => {
      // 定义排序优先级
      const order = [1, 2, 3, 4, 6, 7];
      const aValue = a.dopplerProperty !== undefined ? a.dopplerProperty : (a.wearLevel || 0);
      const bValue = b.dopplerProperty !== undefined ? b.dopplerProperty : (b.wearLevel || 0);
      const aIndex = order.indexOf(aValue);
      const bIndex = order.indexOf(bValue);
      return aIndex - bIndex;
    });

    // 使用API返回的真实市场底价，而不是筛选后商品的底价
    const templatesWithBasePrice = sortedTemplates.map(template => {
      // 使用API返回的floorPrice作为底价，如果没有则计算筛选后商品的最低价
      const basePrice = template.floorPrice || (() => {
        const sortedItems = template.items
          .map(item => ({ ...item, price: parseFloat(item.minSellPrice) || 0 }))
          .filter(item => item.price > 0)
          .sort((a, b) => a.price - b.price);
        return sortedItems.length > 0 ? sortedItems[0].price : 0;
      })();

      return { ...template, basePrice };
    });

    return { templateName, templates: templatesWithBasePrice };
  }).sort((a, b) => {
    // 使用自定义排序函数对分组后的数据进行排序
    const orderA = getTemplateSortOrder(a.templateName);
    const orderB = getTemplateSortOrder(b.templateName);

    // 如果两个都有自定义排序，按优先级排序
    if (orderA !== 999 && orderB !== 999) {
      return orderA - orderB;
    }

    // 如果只有一个有自定义排序，有排序的排在前面
    if (orderA !== 999) return -1;
    if (orderB !== 999) return 1;

    // 如果都没有自定义排序，按字母顺序排序
    return a.templateName.localeCompare(b.templateName);
  });

  const getDopplerColor = (dopplerProperty) => {
    switch (dopplerProperty) {
      case 1: return 'text-orange-600 bg-orange-100'; // Phase 1
      case 2: return 'text-blue-600 bg-blue-100'; // Phase 2
      case 3: return 'text-blue-500 bg-blue-50'; // Phase 3
      case 4: return 'text-purple-600 bg-purple-100'; // Phase 4
      case 5: return 'text-green-600 bg-green-100'; // 绿宝石
      case 6: return 'text-red-600 bg-red-100'; // 红宝石
      case 7: return 'text-blue-800 bg-blue-200'; // 蓝宝石
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  const getDopplerIcon = (dopplerProperty) => {
    switch (dopplerProperty) {
      case 1: return '🔶'; // Phase 1
      case 2: return '💎'; // Phase 2
      case 3: return '🔷'; // Phase 3
      case 4: return '🟣'; // Phase 4
      case 5: return '💚'; // 绿宝石
      case 6: return '❤️'; // 红宝石
      case 7: return '💙'; // 蓝宝石
      default: return '⚪';
    }
  };

  // 如果正在检查认证状态或未登录，显示加载页面
  if (authLoading || !isAuthenticated) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-purple-50 to-pink-50 flex items-center justify-center">
        <div className="text-center bg-white rounded-2xl shadow-xl p-8 max-w-md mx-4">
          <div className="relative">
            <div className="animate-spin rounded-full h-20 w-20 border-4 border-blue-200 border-t-blue-600 mx-auto"></div>
            <div className="absolute inset-0 flex items-center justify-center">
              <span className="text-2xl">🔐</span>
            </div>
          </div>
          <h3 className="mt-6 text-xl font-semibold text-gray-800">验证登录状态</h3>
          <p className="mt-2 text-gray-600">请稍候，正在检查您的登录状态...</p>
        </div>
      </div>
    );
  }

  // 检查是否有会员权限
  const hasAccess = isAuthenticated && hasValidSubscription();
  const subscriptionInfo = getSubscriptionInfo();

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-purple-50 flex items-center justify-center">
        <div className="text-center bg-white rounded-2xl shadow-xl p-8 max-w-md mx-4">
          <div className="relative">
            <div className="animate-spin rounded-full h-20 w-20 border-4 border-blue-200 border-t-blue-600 mx-auto"></div>
            <div className="absolute inset-0 flex items-center justify-center">
              <span className="text-2xl">🔍</span>
            </div>
          </div>
          <h3 className="mt-6 text-xl font-semibold text-gray-800">正在查询{currentTier}等级模板</h3>
          <p className="mt-2 text-gray-600">请稍候，正在获取最新价格数据...</p>
          <div className="mt-4 flex justify-center space-x-1">
            <div className="w-2 h-2 bg-blue-600 rounded-full animate-bounce"></div>
            <div className="w-2 h-2 bg-blue-600 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
            <div className="w-2 h-2 bg-blue-600 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-red-50 to-orange-50 flex items-center justify-center">
        <div className="text-center bg-white rounded-2xl shadow-xl p-8 max-w-md mx-4">
          <div className="text-6xl mb-4">😵</div>
          <h3 className="text-xl font-semibold text-red-600 mb-2">查询失败</h3>
          <p className="text-gray-600 mb-6">{error}</p>
          <button
            onClick={() => fetchTemplates(currentTier)}
            className="px-6 py-3 bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-lg hover:from-blue-700 hover:to-purple-700 transition-all duration-200 transform hover:scale-105 shadow-lg"
          >
            🔄 重新查询
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-purple-50 to-pink-50">
      <div className="container mx-auto px-4 py-8">
        {/* 用户状态栏 */}
        <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-4 mb-6">
          <div className="flex justify-between items-center">
            <div className="flex items-center space-x-4">
              <h1 className="text-2xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                饰品监控助手饰品价格监控
              </h1>
              {isAuthenticated && (
                <span className="text-sm text-gray-600">
                  欢迎回来，{user?.username || user?.email}
                </span>
              )}
            </div>
            
            <div className="flex items-center space-x-3">
              {isAuthenticated ? (
                <>
                  {hasAccess ? (
                    <span className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-green-100 text-green-800">
                      ✅ 会员有效
                      {subscriptionInfo.daysRemaining > 0 && (
                        <span className="ml-1">({subscriptionInfo.daysRemaining}天)</span>
                      )}
                    </span>
                  ) : (
                    <Link href="/subscription">
                      <span className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-red-100 text-red-800 hover:bg-red-200 cursor-pointer transition-colors">
                        ❌ 需要订阅
                      </span>
                    </Link>
                  )}

                  {/* 超级管理员菜单 */}
                  {user?.role === 'super_admin' && (
                    <>
                      <Link href="/admin/users">
                        <button className="px-4 py-2 text-sm bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors">
                          👥 用户管理
                        </button>
                      </Link>
                      <Link href="/admin/payment-confirm">
                        <button className="px-4 py-2 text-sm bg-orange-600 text-white rounded-lg hover:bg-orange-700 transition-colors">
                          🔑 支付确认码
                        </button>
                      </Link>
                    </>
                  )}

                  <Link href="/profile">
                    <button className="px-4 py-2 text-sm bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                      个人中心
                    </button>
                  </Link>
                  <button
                    onClick={logout}
                    className="px-4 py-2 text-sm bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors"
                  >
                    退出登录
                  </button>
                </>
              ) : (
                <>
                  <Link href="/landing">
                    <button className="px-4 py-2 text-sm bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors">
                      📖 了解更多
                    </button>
                  </Link>
                  <Link href="/login">
                    <button className="px-4 py-2 text-sm bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                      登录
                    </button>
                  </Link>
                  <Link href="/register">
                    <button className="px-4 py-2 text-sm bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors">
                      注册
                    </button>
                  </Link>
                </>
              )}
            </div>
          </div>
        </div>

        {/* 非会员提示 - 增强版 */}
        {!hasAccess && (
          <div className="bg-gradient-to-r from-blue-50 to-purple-50 border-2 border-blue-200 rounded-xl p-6 mb-6 shadow-lg">
            <div className="flex flex-col lg:flex-row items-center justify-between gap-4">
              <div className="flex items-start">
                <span className="text-3xl mr-4">�</span>
                <div>
                  <h3 className="text-xl font-bold text-gray-800 mb-1">
                    {isAuthenticated ? '🔥 解锁完整功能，开始赚钱！' : '🚀 免费注册，立即体验专业功能！'}
                  </h3>
                  <p className="text-gray-700 mb-3">
                    {isAuthenticated
                      ? '续费会员即可查看实时价格、接收微信通知，抓住每个投资机会'
                      : '注册后即可预览所有功能，订阅会员解锁实时数据和智能分析'}
                  </p>
                  <div className="flex flex-wrap gap-2 text-sm mb-3">
                    <span className="bg-green-100 text-green-700 px-2 py-1 rounded-full">💰 月收益率20%+</span>
                    <span className="bg-blue-100 text-blue-700 px-2 py-1 rounded-full">⚡ 实时数据更新</span>
                    <span className="bg-purple-100 text-purple-700 px-2 py-1 rounded-full">🔔 微信推送提醒</span>
                    <span className="bg-orange-100 text-orange-700 px-2 py-1 rounded-full">📊 智能溢价分析</span>
                  </div>
                  <p className="text-sm text-gray-600">
                    💡 <strong>算笔账</strong>：一次成功的低溢价交易节省¥850+，而会员费仅需¥20/月！
                  </p>
                </div>
              </div>
              <div className="flex flex-col sm:flex-row gap-3 min-w-fit">
                {!isAuthenticated && (
                  <Link href="/register">
                    <button className="px-6 py-3 bg-gradient-to-r from-green-500 to-blue-600 text-white rounded-lg hover:from-green-600 hover:to-blue-700 transition-all transform hover:scale-105 shadow-lg font-semibold">
                      🎯 免费注册
                    </button>
                  </Link>
                )}
                <Link href="/subscription">
                  <button className="px-6 py-3 bg-gradient-to-r from-purple-600 to-pink-600 text-white rounded-lg hover:from-purple-700 hover:to-pink-700 transition-all transform hover:scale-105 shadow-lg font-semibold">
                    {isAuthenticated ? '💎 立即续费 (¥20/月)' : '🚀 查看定价'}
                  </button>
                </Link>
              </div>
            </div>

            {/* 价值展示 */}
            <div className="mt-4 pt-4 border-t border-blue-200">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-center">
                <div className="bg-white rounded-lg p-3 shadow-sm">
                  <div className="text-lg font-bold text-green-600">¥850+</div>
                  <div className="text-xs text-gray-600">单次交易平均节省</div>
                </div>
                <div className="bg-white rounded-lg p-3 shadow-sm">
                  <div className="text-lg font-bold text-blue-600">30分钟</div>
                  <div className="text-xs text-gray-600">数据更新频率</div>
                </div>
                <div className="bg-white rounded-lg p-3 shadow-sm">
                  <div className="text-lg font-bold text-purple-600">¥20/月</div>
                  <div className="text-xs text-gray-600">会员费用</div>
                </div>
              </div>
            </div>
          </div>
        )}

        <div className="mb-8">
          {/* 等级Tab切换 */}
          <div className="flex space-x-1 mb-6">
            <button
              onClick={() => handleTierChange('T1')}
              className={`px-6 py-3 rounded-lg font-medium transition-all duration-200 ${currentTier === 'T1'
                  ? 'bg-blue-600 text-white shadow-lg transform scale-105'
                  : 'bg-white text-gray-600 hover:bg-gray-50 hover:text-blue-600 border border-gray-200'
                }`}
            >
              <span className="flex items-center gap-2">
                <span className="text-lg">👑</span>
                T1等级 (顶级)
              </span>
            </button>
            <button
              onClick={() => handleTierChange('T2')}
              className={`px-6 py-3 rounded-lg font-medium transition-all duration-200 ${currentTier === 'T2'
                  ? 'bg-purple-600 text-white shadow-lg transform scale-105'
                  : 'bg-white text-gray-600 hover:bg-gray-50 hover:text-purple-600 border border-gray-200'
                }`}
            >
              <span className="flex items-center gap-2">
                <span className="text-lg">💎</span>
                T2等级 (高级)
              </span>
            </button>
          </div>

          {/* 搜索和刷新模块 */}
          <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-4 mb-6">
            <div className="flex flex-col sm:flex-row gap-4 items-center">
              {/* 带模糊搜索的饰品类型选择器 */}
              <div className="flex-1 relative search-dropdown">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none z-10">
                  <span className="text-gray-400 text-lg">🔍</span>
                </div>

                {/* 搜索输入框 */}
                <input
                  type="text"
                  placeholder={hasAccess 
                    ? (searchQuery || "搜索或选择饰品类型 (如: AK-47, M4A4, 刺刀...)") 
                    : "高级搜索功能需要会员订阅"
                  }
                  value={searchInput}
                  onChange={(e) => {
                    if (hasAccess) {
                      setSearchInput(e.target.value);
                      setIsDropdownOpen(true);
                    }
                  }}
                  onFocus={() => hasAccess && setIsDropdownOpen(true)}
                  disabled={!hasAccess}
                  className={`w-full pl-10 pr-20 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 ${
                    hasAccess ? 'bg-white' : 'bg-gray-100 cursor-not-allowed'
                  }`}
                />

                {/* 右侧按钮组 */}
                <div className="absolute inset-y-0 right-0 flex items-center">
                  {searchQuery && (
                    <button
                      onClick={handleClearSearch}
                      className="px-2 text-gray-400 hover:text-gray-600 transition-colors"
                      title="清除选择"
                    >
                      <span className="text-lg">✕</span>
                    </button>
                  )}
                  <button
                    onClick={() => setIsDropdownOpen(!isDropdownOpen)}
                    className="px-3 text-gray-400 hover:text-gray-600 transition-colors"
                  >
                    <span className={`text-lg transition-transform ${isDropdownOpen ? 'rotate-180' : ''}`}>▼</span>
                  </button>
                </div>

                {/* 下拉选项列表 */}
                {isDropdownOpen && hasAccess && (
                  <div className="absolute top-full left-0 right-0 mt-1 bg-white border border-gray-300 rounded-lg shadow-lg z-20 max-h-60 overflow-y-auto">
                    {/* 全部选项 */}
                    <div
                      onClick={() => handleSelectTemplate('')}
                      className="px-4 py-3 hover:bg-blue-50 cursor-pointer border-b border-gray-100 text-gray-600"
                    >
                      <span className="flex items-center gap-2">
                        <span>🎯</span>
                        <span>全部饰品类型</span>
                      </span>
                    </div>

                    {/* 过滤后的饰品类型 */}
                    {filteredTemplates.length > 0 ? (
                      filteredTemplates.map((templateName) => (
                        <div
                          key={templateName}
                          onClick={() => handleSelectTemplate(templateName)}
                          className={`px-4 py-3 hover:bg-blue-50 cursor-pointer border-b border-gray-100 last:border-b-0 ${searchQuery === templateName ? 'bg-blue-100 text-blue-700' : 'text-gray-700'
                            }`}
                        >
                          <span className="flex items-center gap-2">
                            <span>🗡️</span>
                            <span>{templateName}</span>
                          </span>
                        </div>
                      ))
                    ) : (
                      <div className="px-4 py-3 text-gray-500 text-center">
                        <span className="flex items-center justify-center gap-2">
                          <span>🔍</span>
                          <span>未找到匹配的饰品类型</span>
                        </span>
                      </div>
                    )}
                  </div>
                )}
              </div>

              {/* 刷新按钮和最后更新时间 */}
              <div className="flex items-center gap-4">
                <div className="text-sm text-gray-500">
                  <div className="flex items-center gap-2">
                    <span>最后更新: {hasAccess ? lastRefreshTime.toLocaleTimeString() : '需要会员订阅'}</span>
                    {dataLoading && (
                      <span className="inline-flex items-center px-2 py-1 rounded-full text-xs bg-blue-100 text-blue-600">
                        <span className="animate-spin mr-1">🔄</span>
                        更新中
                      </span>
                    )}
                  </div>
                  <div className="text-xs">{hasAccess ? '自动刷新: 半点/整点' : '会员专享功能'}</div>
                  {hasAccess && nextRefreshTime && (
                    <div className="text-xs text-blue-600">
                      下次刷新: {nextRefreshTime.toLocaleTimeString()}
                    </div>
                  )}
                </div>
                <div className="flex gap-2">
                  <button
                    onClick={handleManualRefresh}
                    disabled={dataLoading || !hasAccess}
                    className={`px-4 py-3 rounded-lg transition-all duration-200 transform hover:scale-105 shadow-lg disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none ${
                      hasAccess
                        ? 'bg-gradient-to-r from-green-500 to-blue-500 text-white hover:from-green-600 hover:to-blue-600'
                        : 'bg-gray-300 text-gray-500 cursor-not-allowed'
                    }`}
                  >
                    <span className="flex items-center gap-2">
                      <span className={dataLoading ? 'animate-spin' : ''}>{hasAccess ? '🔄' : '🔒'}</span>
                      {dataLoading ? '刷新中...' : '手动刷新'}
                      {!hasAccess && <span className="text-xs">(会员专享)</span>}
                    </span>
                  </button>



                  <button
                    onClick={hasAccess ? handlePremiumQuery : () => alert('此功能需要会员订阅，请先登录并订阅')}
                    disabled={dataLoading || !hasAccess}
                    className={`px-4 py-3 rounded-lg transition-all duration-200 transform hover:scale-105 shadow-lg disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none ${
                      hasAccess 
                        ? 'bg-gradient-to-r from-purple-500 to-pink-500 text-white hover:from-purple-600 hover:to-pink-600' 
                        : 'bg-gray-300 text-gray-500 cursor-not-allowed'
                    }`}
                    title={hasAccess ? "查询内置目标商品中溢价10%以内的商品并发送通知" : "此功能需要会员订阅"}
                  >
                    <span className="flex items-center gap-2">
                      <span>{hasAccess ? '💎' : '🔒'}</span>
                      {dataLoading ? '查询中...' : '溢价筛选'}
                      {!hasAccess && <span className="text-xs">(会员专享)</span>}
                    </span>
                  </button>


                </div>
              </div>
            </div>

            {/* 搜索结果统计 */}
            {searchQuery && (
              <div className="mt-3 pt-3 border-t border-gray-100">
                <div className="text-sm text-gray-600">
                  已选择饰品类型: <span className="font-medium text-blue-600">{searchQuery}</span> - 找到 {filteredData.length} 个多普勒变体
                </div>
              </div>
            )}
          </div>

          <div className="text-sm text-gray-500">
            当前等级: <span className="font-medium text-gray-700">{currentTier}</span> |
            共找到 {groupedData.length} 个饰品模板，{data.length} 个多普勒变体
            {premiumItemsCount > 0 && (
              <span className="ml-2 px-2 py-1 bg-red-100 text-red-600 rounded-full text-xs font-medium">
                💎 {premiumItemsCount} 个低溢价商品
              </span>
            )}
          </div>
        </div>

        {/* 按模板分组展示 */}
        {groupedData.map(({ templateName, templates }) => (
          <div key={templateName} className="bg-white rounded-lg shadow-md overflow-hidden mb-8">
            {/* 模板标题 */}
            <div className="bg-gradient-to-r from-blue-600 to-purple-600 text-white p-4">
              <h2 className="text-xl font-bold flex items-center gap-2">
                🗡️ {templateName}
              </h2>
            </div>

            {/* 一行展示4种多普勒属性 */}
            <div className="p-6">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                {templates.map((template) => {
                  // 使用 dopplerProperty 或 wearLevel 来创建唯一的 key
                  const uniqueId = template.dopplerProperty !== undefined ? template.dopplerProperty : template.wearLevel;
                  const sectionKey = `${templateName}-${uniqueId}`;
                  const isExpanded = expandedSections[sectionKey];

                  // 获取过滤后的商品
                  const filteredItems = getFilteredItems(template.items, templateName, uniqueId || 0);
                  const sortedItems = filteredItems.sort((a, b) => parseFloat(a.minSellPrice) - parseFloat(b.minSellPrice));
                  const displayItems = isExpanded ? sortedItems : sortedItems.slice(0, 5);

                  return (
                    <div key={sectionKey} className="border border-gray-200 rounded-xl overflow-hidden shadow-sm hover:shadow-md transition-all duration-200 bg-white">
                      {/* 多普勒类型标题 */}
                      <div className={`p-4 ${getDopplerColor(template.dopplerProperty || 0)} border-b`}>
                        <div className="flex items-center justify-between">
                          <div className="flex items-center gap-2">
                            <span className="text-xl">{getDopplerIcon(template.dopplerProperty || 0)}</span>
                            <div className="flex flex-col">
                              <span className="font-bold text-sm">{template.dopplerName}</span>
                              <span className="text-xs opacity-90 font-medium">
                                底价: ¥{template.basePrice > 0 ? template.basePrice.toFixed(2) : '暂无'}
                              </span>
                            </div>
                          </div>
                          <div className="flex items-center gap-2">
                            <div className="text-xs opacity-75 bg-white/20 px-2 py-1 rounded-full">
                              {filteredItems.length} / {template.items.length} 个
                            </div>
                          </div>
                        </div>

                        {/* Phase专属搜索功能 */}
                        {hasAccess && (
                          <div className="flex gap-2 mt-3">
                            {/* 图案模板输入框 */}
                            <div className="flex-1">
                              <input
                                type="text"
                                placeholder="输入模板号"
                                value={phaseFilters[`${templateName}-${uniqueId}`]?.templateFilter || ''}
                                onChange={(e) => updatePhaseFilter(templateName, uniqueId || 0, 'template', e.target.value)}
                                className="w-full bg-white/90 text-gray-800 px-2 py-1.5 rounded-lg text-xs border border-white/30 focus:border-blue-400 focus:outline-none placeholder-gray-500"
                              />
                            </div>

                          {/* 磨损区间下拉框 */}
                          <div className="flex-1">
                            <select
                              value={phaseFilters[`${templateName}-${uniqueId}`]?.wearFilter || ''}
                              onChange={(e) => updatePhaseFilter(templateName, uniqueId || 0, 'wear', e.target.value)}
                              className="w-full bg-white/90 text-gray-800 px-2 py-1.5 rounded-lg text-xs border border-white/30 focus:border-blue-400 focus:outline-none"
                            >
                              <option value="">全部磨损</option>
                              {(() => {
                                // 获取当前模板的所有磨损值
                                const wearValues = template.items
                                  .map(item => parseFloat(item.abrade || '0'))
                                  .filter(wear => !isNaN(wear))
                                  .sort((a, b) => a - b);

                                if (wearValues.length === 0) return null;

                                const minWear = Math.min(...wearValues);
                                const maxWear = Math.max(...wearValues);

                                const wearOptions = [];

                                // 崭新出厂 (0.00-0.07)
                                if (minWear < 0.07) {
                                  wearOptions.push(
                                    ...[
                                      <option key="0.00-0.01" value="0.00-0.01">崭新出厂 (0.00-0.01)</option>,
                                      <option key="0.01-0.02" value="0.01-0.02">崭新出厂 (0.01-0.02)</option>,
                                      <option key="0.02-0.03" value="0.02-0.03">崭新出厂 (0.02-0.03)</option>,
                                      <option key="0.03-0.04" value="0.03-0.04">崭新出厂 (0.03-0.04)</option>,
                                      <option key="0.04-0.07" value="0.04-0.07">崭新出厂 (0.04-0.07)</option>
                                    ]
                                  );
                                }

                                // 略有磨损 (0.07-0.15)
                                if (maxWear >= 0.07 && minWear < 0.15) {
                                  wearOptions.push(
                                    ...[
                                      <option key="0.07-0.08" value="0.07-0.08">略有磨损 (0.07-0.08)</option>,
                                      <option key="0.08-0.09" value="0.08-0.09">略有磨损 (0.08-0.09)</option>,
                                      <option key="0.09-0.10" value="0.09-0.10">略有磨损 (0.09-0.10)</option>,
                                      <option key="0.10-0.11" value="0.10-0.11">略有磨损 (0.10-0.11)</option>,
                                      <option key="0.11-0.15" value="0.11-0.15">略有磨损 (0.11-0.15)</option>
                                    ]
                                  );
                                }

                                // 久经沙场 (0.15-0.38)
                                if (maxWear >= 0.15 && minWear < 0.38) {
                                  wearOptions.push(
                                    ...[
                                      <option key="0.15-0.18" value="0.15-0.18">久经沙场 (0.15-0.18)</option>,
                                      <option key="0.18-0.21" value="0.18-0.21">久经沙场 (0.18-0.21)</option>,
                                      <option key="0.21-0.24" value="0.21-0.24">久经沙场 (0.21-0.24)</option>,
                                      <option key="0.24-0.27" value="0.24-0.27">久经沙场 (0.24-0.27)</option>,
                                      <option key="0.27-0.38" value="0.27-0.38">久经沙场 (0.27-0.38)</option>
                                    ]
                                  );
                                }

                                // 破损不堪 (0.38-0.45)
                                if (maxWear >= 0.38 && minWear < 0.45) {
                                  wearOptions.push(
                                    ...[
                                      <option key="0.38-0.39" value="0.38-0.39">破损不堪 (0.38-0.39)</option>,
                                      <option key="0.39-0.40" value="0.39-0.40">破损不堪 (0.39-0.40)</option>,
                                      <option key="0.40-0.41" value="0.40-0.41">破损不堪 (0.40-0.41)</option>,
                                      <option key="0.41-0.42" value="0.41-0.42">破损不堪 (0.41-0.42)</option>,
                                      <option key="0.42-0.45" value="0.42-0.45">破损不堪 (0.42-0.45)</option>
                                    ]
                                  );
                                }

                                // 战痕累累 (0.45-1.00)
                                if (maxWear >= 0.45) {
                                  wearOptions.push(
                                    ...[
                                      <option key="0.45-0.50" value="0.45-0.50">战痕累累 (0.45-0.50)</option>,
                                      <option key="0.50-0.63" value="0.50-0.63">战痕累累 (0.50-0.63)</option>,
                                      <option key="0.63-0.76" value="0.63-0.76">战痕累累 (0.63-0.76)</option>,
                                      <option key="0.76-0.90" value="0.76-0.90">战痕累累 (0.76-0.90)</option>,
                                      <option key="0.90-1.00" value="0.90-1.00">战痕累累 (0.90-1.00)</option>
                                    ]
                                  );
                                }

                                return wearOptions;
                              })()}
                            </select>
                          </div>
                        </div>
                        )}
                      </div>

                      {/* 商品列表 */}
                      <div className="p-1">
                        {template.items.length > 0 ? (
                          <div className="space-y-2">
                            {displayItems.map((item, index) => {
                              const itemPrice = parseFloat(item.minSellPrice);
                              // 创建更唯一的key，包含更多标识信息
                              const uniqueKey = `${item.templateId}-${item.paintSeed || 'no-seed'}-${item.abrade || 'no-abrade'}-${index}`;

                              return (
                                <div key={uniqueKey} className="bg-gradient-to-r from-gray-50 to-gray-100 rounded-lg text-sm hover:from-blue-50 hover:to-purple-50 transition-all duration-200 border border-gray-100 hover:border-blue-200 overflow-hidden">
                                  {/* 一行四列布局 */}
                                  <div className="grid grid-cols-4 gap-2 p-1 items-center">
                                    {/* 模板 */}
                                    <div className="text-center">
                                      <div className="text-xs text-gray-500 mb-1">模板</div>
                                      <div className="font-bold text-blue-600 text-base">
                                        {item.paintSeed || item.templateId}
                                      </div>
                                    </div>

                                    {/* 价格 */}
                                    <div className="text-center">
                                      <div className="text-xs text-gray-500 mb-1">价格</div>
                                      <div className="font-bold text-green-600 text-base">
                                        ¥{itemPrice.toFixed(2)}
                                      </div>
                                    </div>

                                    {/* 磨损 */}
                                    <div className="text-center">
                                      <div className="text-xs text-gray-500 mb-1">磨损</div>
                                      <div className="font-semibold text-orange-600 text-sm">
                                        {item.abrade ? parseFloat(item.abrade).toFixed(4) : 'N/A'}
                                      </div>
                                    </div>

                                    {/* 溢价 */}
                                    <div className="text-center">
                                      <div className="text-xs text-gray-500 mb-1">溢价</div>
                                      <div className="font-semibold text-purple-600 text-sm">
                                        {(() => {
                                          const floorPrice = template.basePrice || 0;
                                          const premium = Math.round(itemPrice - floorPrice);
                                          return premium > 0 ? `+${premium}` : premium.toString();
                                        })()}
                                      </div>
                                    </div>
                                  </div>
                                </div>
                              );
                            })}

                            {/* 更多按钮 */}
                            {template.items.length > 5 && (
                              <button
                                onClick={() => toggleExpanded(sectionKey)}
                                className="w-full py-3 text-sm font-medium text-blue-600 hover:text-blue-800 hover:bg-gradient-to-r hover:from-blue-50 hover:to-purple-50 rounded-lg transition-all duration-200 border border-blue-200 hover:border-blue-300 mt-2"
                              >
                                <span className="flex items-center justify-center gap-2">
                                  <span>{isExpanded ? '🔼' : '🔽'}</span>
                                  {isExpanded
                                    ? `收起 (显示前5条)`
                                    : `查看更多 (还有${template.items.length - 5}条)`
                                  }
                                </span>
                              </button>
                            )}
                          </div>
                        ) : (
                          <div className="text-center py-8 text-gray-500">
                            {!hasAccess ? (
                              <div className="bg-gradient-to-br from-blue-50 to-purple-50 rounded-lg p-6 border-2 border-dashed border-blue-200">
                                <div className="text-4xl mb-3">💎</div>
                                <div className="text-sm font-bold text-gray-800 mb-2">解锁实时价格数据</div>
                                <div className="text-xs text-gray-600 mb-3">
                                  查看真实商品价格、溢价分析<br/>
                                  发现投资机会，获得收益
                                </div>
                                <Link href="/subscription">
                                  <button className="px-4 py-2 bg-gradient-to-r from-purple-600 to-pink-600 text-white text-xs rounded-lg hover:from-purple-700 hover:to-pink-700 transition-all transform hover:scale-105 shadow-md font-semibold">
                                    ¥20/月 解锁
                                  </button>
                                </Link>
                              </div>
                            ) : dataLoading ? (
                              <>
                                <div className="text-3xl mb-2">
                                  <span className="animate-spin">🔄</span>
                                </div>
                                <div className="text-sm">正在加载商品数据...</div>
                              </>
                            ) : (
                              <>
                                <div className="text-3xl mb-2">📦</div>
                                <div className="text-sm">暂无在售商品</div>
                              </>
                            )}
                          </div>
                        )}
                      </div>
                    </div>
                  );
                })}
              </div>
            </div>
          </div>
        ))}

        {groupedData.length === 0 && (
          <div className="text-center py-16">
            <div className="bg-white rounded-2xl shadow-lg p-12 max-w-md mx-auto">
              {!hasAccess ? (
                <>
                  <div className="text-6xl mb-4">🔒</div>
                  <h3 className="text-xl font-semibold text-gray-800 mb-2">需要会员订阅</h3>
                  <p className="text-gray-600 mb-6">订阅会员后即可查看{currentTier}等级的饰品数据和价格信息</p>
                  <Link href="/subscription">
                    <button className="px-6 py-3 bg-gradient-to-r from-purple-600 to-pink-600 text-white rounded-lg hover:from-purple-700 hover:to-pink-700 transition-all duration-200 transform hover:scale-105 shadow-lg">
                      💎 立即订阅
                    </button>
                  </Link>
                </>
              ) : dataLoading ? (
                <>
                  <div className="text-6xl mb-4">
                    <span className="animate-spin">🔄</span>
                  </div>
                  <h3 className="text-xl font-semibold text-gray-800 mb-2">正在加载{currentTier}等级数据</h3>
                  <p className="text-gray-600 mb-6">正在获取最新的饰品价格信息，请稍候...</p>
                  <div className="flex items-center justify-center">
                    <div className="animate-pulse flex space-x-1">
                      <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                      <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                      <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                    </div>
                  </div>
                </>
              ) : (
                <>
                  <div className="text-6xl mb-4">🎮</div>
                  <h3 className="text-xl font-semibold text-gray-800 mb-2">暂无{currentTier}等级数据</h3>
                  <p className="text-gray-600 mb-6">当前等级下没有找到饰品数据，请尝试切换其他等级</p>
                  <button
                    onClick={() => fetchTemplates(currentTier)}
                    className="px-6 py-3 bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-lg hover:from-blue-700 hover:to-purple-700 transition-all duration-200 transform hover:scale-105 shadow-lg"
                  >
                    🔄 刷新数据
                  </button>
                </>
              )}
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
